{"key": "plugin_content_manager_configuration_components::sections.articles", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "title", "defaultSortBy": "title", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "title": {"edit": {"label": "<PERSON><PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "text": {"edit": {"label": "Text", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "text", "searchable": true, "sortable": true}}, "showAll": {"edit": {"label": "Zobraziť všetky", "description": "Ak je toto pole TRUE, zobrazia sa všetky články spolu s filtrovaním. Ak je FALSE, zobrazia sa články, ktoré spĺňajú kritériá v poliach nižšie.", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "showAll", "searchable": true, "sortable": true}}, "category": {"edit": {"label": "Typ (toto pole prosí<PERSON>, bude odstrán<PERSON>)", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "shortTitle"}, "list": {"label": "category", "searchable": true, "sortable": true}}, "articles": {"edit": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Umožní pripnúť konkrétne články. Články z tohto poľa sa zobrazia ako prvé. Ak sú vyplnené aj polia nižšie (Typy, Témy, Ad<PERSON> s<PERSON>), za pripnutými článkami sa zobrazia články, pre ktoré platia hodnoty zvyšných polí súčasne.", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "articles", "searchable": false, "sortable": false}}, "articleCategories": {"edit": {"label": "<PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "articleCategories", "searchable": false, "sortable": false}}, "tags": {"edit": {"label": "<PERSON><PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "tags", "searchable": false, "sortable": false}}, "adminGroups": {"edit": {"label": "<PERSON><PERSON> s<PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "adminGroups", "searchable": false, "sortable": false}}, "showMoreLink": {"edit": {"label": "showMoreLink", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "label"}, "list": {"label": "showMoreLink", "searchable": false, "sortable": false}}, "documentId": {"edit": {}, "list": {"label": "documentId", "searchable": true, "sortable": true}}}, "layouts": {"edit": [[{"name": "title", "size": 12}], [{"name": "text", "size": 12}], [{"name": "showAll", "size": 12}], [{"name": "articles", "size": 6}], [{"name": "articleCategories", "size": 6}], [{"name": "tags", "size": 6}], [{"name": "adminGroups", "size": 6}], [{"name": "category", "size": 6}], [{"name": "showMoreLink", "size": 12}]], "list": ["id", "title", "text", "category"]}, "uid": "sections.articles", "isComponent": true}, "type": "object", "environment": null, "tag": null}