{"collectionName": "components_sections_articles", "info": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "options": {}, "attributes": {"title": {"type": "string"}, "text": {"type": "text"}, "showAll": {"type": "boolean", "default": false}, "category": {"type": "relation", "relation": "oneToOne", "target": "api::page-category.page-category", "conditions": {"visible": {"!=": [{"var": "showAll"}, true]}}}, "articles": {"type": "relation", "relation": "oneToMany", "target": "api::article.article", "conditions": {"visible": {"!=": [{"var": "showAll"}, true]}}}, "articleCategories": {"type": "relation", "relation": "oneToMany", "target": "api::article-category.article-category", "conditions": {"visible": {"!=": [{"var": "showAll"}, true]}}}, "tags": {"type": "relation", "relation": "oneToMany", "target": "api::tag.tag", "conditions": {"visible": {"!=": [{"var": "showAll"}, true]}}}, "adminGroups": {"type": "relation", "relation": "oneToMany", "target": "api::admin-group.admin-group", "conditions": {"visible": {"!=": [{"var": "showAll"}, true]}}}, "showMoreLink": {"type": "component", "conditions": {"visible": {"!=": [{"var": "showAll"}, true]}}, "component": "blocks.common-link", "repeatable": false}}}